package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Category Management - Updated for normalized structure
func (m mongoDB) CreateCategory(ctx context.Context, category *dreamboard.Category) error {
	category.ObjectID = primitive.NewObjectID()

	_, err := m.categoriesCollection.InsertOne(ctx, category)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.NewConflictError(errors.Repository, "category already exists", errors.KeyDreamboardErrorCategoryAddFailed, err)
		}
		return errors.NewInternalError(errors.Repository, "failed to create category", errors.KeyDreamboardErrorCategoryAddFailed, err)
	}

	category.ID = category.ObjectID.Hex()
	return nil
}

func (m mongoDB) CreateCategories(ctx context.Context, categories []*dreamboard.Category) error {
	if len(categories) == 0 {
		return nil
	}

	// Prepare documents for insertion
	var documents []interface{}
	for _, category := range categories {
		category.ObjectID = primitive.NewObjectID()
		category.ID = category.ObjectID.Hex()
		documents = append(documents, category)
	}

	_, err := m.categoriesCollection.InsertMany(ctx, documents)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to create categories", errors.KeyDreamboardErrorCategoryAddFailed, err)
	}

	return nil
}

func (m mongoDB) FindCategory(ctx context.Context, categoryID primitive.ObjectID) (*dreamboard.Category, error) {
	var category dreamboard.Category
	err := m.categoriesCollection.FindOne(ctx, bson.M{"_id": categoryID}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find category", errors.KeyDreamboardErrorCategoryFindFailed, err)
	}

	category.ID = category.ObjectID.Hex()
	return &category, nil
}

func (m mongoDB) UpdateCategory(ctx context.Context, category *dreamboard.Category) error {
	result, err := m.categoriesCollection.UpdateOne(ctx,
		bson.M{"_id": category.ObjectID},
		bson.M{"$set": category})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to update category", errors.KeyDreamboardErrorCategoryUpdateFailed, err)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, nil)
	}
	return nil
}

func (m mongoDB) DeleteCategory(ctx context.Context, categoryID primitive.ObjectID) error {
	result, err := m.categoriesCollection.DeleteOne(ctx, bson.M{"_id": categoryID})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "failed to delete category", errors.KeyDreamboardErrorCategoryDeleteFailed, err)
	}
	if result.DeletedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, nil)
	}
	return nil
}

// FindCategories finds all categories (can be cached for performance)
func (m mongoDB) FindCategories(ctx context.Context) ([]*dreamboard.Category, error) {
	cursor, err := m.categoriesCollection.Find(ctx, bson.M{})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "categories find failed", errors.KeyDreamboardErrorCategoryFindFailed, err)
	}
	defer cursor.Close(ctx)

	var categories []*dreamboard.Category
	for cursor.Next(ctx) {
		var category dreamboard.Category
		if err := cursor.Decode(&category); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "category decode failed", errors.KeyDreamboardErrorCategoryFindFailed, err)
		}
		category.ID = category.ObjectID.Hex()
		categories = append(categories, &category)
	}

	return categories, nil
}

// FindCategoryByIdentifier finds a category by its identifier
func (m mongoDB) FindCategoryByIdentifier(ctx context.Context, identifier string) (*dreamboard.Category, error) {
	var category dreamboard.Category
	err := m.categoriesCollection.FindOne(ctx, bson.M{"identifier": identifier}).Decode(&category)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "category not found", errors.KeyDreamboardErrorCategoryNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "failed to find category", errors.KeyDreamboardErrorCategoryFindFailed, err)
	}

	category.ID = category.ObjectID.Hex()
	return &category, nil
}
