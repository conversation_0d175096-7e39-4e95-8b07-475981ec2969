package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"

	_dreamboard "github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// CRUD
	Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error)
	Find(ctx context.Context, id string) (*dreamboard.Dreamboard, error)
	FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error)
	FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error)
	Update(ctx context.Context, board *dreamboard.Dreamboard) error
	Delete(ctx context.Context, id string) error

	// DTO methods for normalized structure (maintaining API compatibility)
	FindDTO(ctx context.Context, id string) (*dreamboard.DreamboardDTO, error)
	FindAllDTO(ctx context.Context) ([]*dreamboard.DreamboardDTO, error)
	FindAllByUsersDTO(ctx context.Context, userIDs []string) ([]*dreamboard.DreamboardDTO, error)
	FindByUserDTO(ctx context.Context, userID string) (*dreamboard.DreamboardDTO, error)

	// Category CRUD
	CreateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error)
	FindCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) (*dreamboard.Category, error)
	UpdateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error)
	DeleteCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) error

	// Dream CRUD
	CreateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) (*CreateDreamResponseDTO, error)
	FindDream(ctx context.Context, board *dreamboard.Dreamboard, dreamID string) (*dreamboard.Dream, error)
	FindPersonalDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error)
	FindSharedDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error)
	FindDreamDetails(ctx context.Context, dreamID string, userID string) (*DreamDetails, error)
	UpdateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error)
	RemoveDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error)

	// Dream Management
	CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error)

	// Invitation Management
	InviteDetails(ctx context.Context, code string) (*InviteDetailsDTO, error)
	JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*dreamboard.Contribution, error)

	// Share Link Management
	CreateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)
	FindShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)
	FindShareLinkByCode(ctx context.Context, code string) (*dreamboard.ShareLink, error)
	UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error
	RegenerateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)

	// Contribution Management
	CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (*dreamboard.Contribution, error)
	FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error)
	FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error)
	UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error
	UpdateContributionStatus(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error
	DeleteContribution(ctx context.Context, contributionID string) error

	// Utility
	Initialize(ctx context.Context, userID string) error
}

type service struct {
	Repository               _dreamboard.Repository
	FinancialSheetRepository financialsheet.Repository
	GamificationService      gamification.Service
}

func New(repository _dreamboard.Repository, financialSheetRepository financialsheet.Repository, gamificationService gamification.Service) Service {
	return &service{
		Repository:               repository,
		FinancialSheetRepository: financialSheetRepository,
		GamificationService:      gamificationService,
	}
}

// Helper method to assemble dreamboard with dreams and categories for backward compatibility
func (s *service) assembleDreamboardDTO(ctx context.Context, board *dreamboard.Dreamboard) (*dreamboard.DreamboardDTO, error) {
	// Get dreams for this dreamboard
	dreams, err := s.Repository.FindDreamsByDreamboardID(ctx, board.ObjectID)
	if err != nil {
		return nil, err
	}

	// Get all categories
	categories, err := s.Repository.FindCategories(ctx)
	if err != nil {
		return nil, err
	}

	// Get financial sheet for calculations
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, board.User)
	if err != nil {
		return nil, err
	}

	// Create DTO
	dto := &dreamboard.DreamboardDTO{}
	dto.FromDreamboard(board, dreams, categories)

	// Set IDs for response
	if dto.ObjectID.IsZero() == false {
		dto.ID = dto.ObjectID.Hex()
	}

	for _, dream := range dto.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Update computed fields
	board.CalculateSavedAmount(financialsheet, dreams)
	dto.SavedAmount = board.SavedAmount
	dto.ComputeTotals(dto.Dreams)

	return dto, nil
}

// CRUD
func (s *service) Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error) {
	currentTime := time.Now()
	board.CreatedAt = currentTime
	board.UpdatedAt = currentTime
	// Initialize computed fields to zero since there are no dreams yet
	board.TotalDreamsCost = 0
	board.SavedAmount = 0
	board.MonthlyNeeded = 0
	board.RemainingAmount = 0

	dreamboardID, err := s.Repository.Create(ctx, board)
	if err != nil {
		return "", err
	}

	return dreamboardID, err
}

func (s *service) Find(ctx context.Context, id string) (*dreamboard.Dreamboard, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.NewWithTranslationKey(errors.Service, "invalid dreamboard id", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	board, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	dto, err := s.assembleDreamboardDTO(ctx, board)
	if err != nil {
		return nil, err
	}

	// Convert DTO back to Dreamboard for return (maintaining API compatibility)
	result := dto.ToDreamboard()
	result.ID = dto.ID

	return result, nil
}

func (s *service) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	boards, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	var result []*dreamboard.Dreamboard
	for _, board := range boards {
		// Use assembleDreamboardDTO to get normalized data
		dto, err := s.assembleDreamboardDTO(ctx, board)
		if err != nil {
			return nil, err
		}

		// Convert DTO back to Dreamboard for return (maintaining API compatibility)
		dreamboardResult := dto.ToDreamboard()
		dreamboardResult.ID = dto.ID

		result = append(result, dreamboardResult)
	}

	return result, nil
}

// DTO methods for normalized structure (maintaining API compatibility)
func (s *service) FindDTO(ctx context.Context, id string) (*dreamboard.DreamboardDTO, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.NewWithTranslationKey(errors.Service, "invalid dreamboard id", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	board, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	return s.assembleDreamboardDTO(ctx, board)
}

func (s *service) FindAllDTO(ctx context.Context) ([]*dreamboard.DreamboardDTO, error) {
	boards, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	var result []*dreamboard.DreamboardDTO
	for _, board := range boards {
		dto, err := s.assembleDreamboardDTO(ctx, board)
		if err != nil {
			return nil, err
		}
		result = append(result, dto)
	}

	return result, nil
}

func (s *service) FindAllByUsersDTO(ctx context.Context, userIDs []string) ([]*dreamboard.DreamboardDTO, error) {
	boards, err := s.Repository.FindAllByUsers(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	var result []*dreamboard.DreamboardDTO
	for _, board := range boards {
		dto, err := s.assembleDreamboardDTO(ctx, board)
		if err != nil {
			return nil, err
		}
		result = append(result, dto)
	}

	return result, nil
}

func (s *service) FindByUserDTO(ctx context.Context, userID string) (*dreamboard.DreamboardDTO, error) {
	board, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
		// Initialize new dreamboard for user
		if err := s.Initialize(ctx, userID); err != nil {
			return nil, err
		}

		// Fetch the newly created dreamboard
		board, err = s.Repository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	return s.assembleDreamboardDTO(ctx, board)
}

func (s *service) FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error) {
	boards, err := s.Repository.FindAllByUsers(ctx, userIDs)
	if err != nil {
		return nil, err
	}

	var result []*dreamboard.Dreamboard
	for _, board := range boards {
		// Use assembleDreamboardDTO to get normalized data
		dto, err := s.assembleDreamboardDTO(ctx, board)
		if err != nil {
			return nil, err
		}

		// Convert DTO back to Dreamboard for return (maintaining API compatibility)
		dreamboardResult := dto.ToDreamboard()
		dreamboardResult.ID = dto.ID

		result = append(result, dreamboardResult)
	}

	return result, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	board, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
		// Initialize new dreamboard for user
		if err := s.Initialize(ctx, userID); err != nil {
			return nil, err
		}

		// Fetch the newly created dreamboard
		board, err = s.Repository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Use assembleDreamboardDTO to get normalized data
	dto, err := s.assembleDreamboardDTO(ctx, board)
	if err != nil {
		return nil, err
	}

	// Convert DTO back to Dreamboard for return (maintaining API compatibility)
	result := dto.ToDreamboard()
	result.ID = dto.ID

	return result, nil
}

func (s *service) Update(ctx context.Context, board *dreamboard.Dreamboard) error {
	if err := board.Validate(); err != nil {
		return err
	}

	if board.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(board.ID)
		if err != nil {
			return errors.NewValidationError(errors.Service, "invalid dreamboard ID", errors.KeyDreamboardErrorInvalidId, err)
		}
		board.ObjectID = objID
	}

	board.UpdatedAt = time.Now()

	// Get dreams to compute totals
	dreams, err := s.Repository.FindActiveDreamsByDreamboardID(ctx, board.ObjectID)
	if err != nil {
		return err
	}

	board.ComputeTotals(dreams) // Update computed fields before saving

	err = s.Repository.Update(ctx, board)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.NewWithTranslationKey(errors.Service, "invalid dreamboard ID", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, err)
	}

	return s.Repository.Delete(ctx, objID)
}

// Utility
func (s *service) Initialize(ctx context.Context, userID string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return err
	}

	if existing != nil {
		return errors.NewConflictError(errors.Service, "dreamboard already exists", errors.KeyDreamboardErrorAlreadyExists, nil)
	}

	currentTime := time.Now()
	newBoard := &dreamboard.Dreamboard{
		User:      userID,
		CreatedAt: currentTime,
		UpdatedAt: currentTime,
		// Initialize computed fields to zero since there are no dreams
		TotalDreamsCost: 0,
		SavedAmount:     0,
		MonthlyNeeded:   0,
		RemainingAmount: 0,
	}

	if err := newBoard.Validate(); err != nil {
		return err
	}

	_, err = s.Repository.Create(ctx, newBoard)
	if err != nil {
		return err
	}

	// Add default categories
	categories := []*dreamboard.Category{
		&dreamboard.Category{Identifier: dreamboard.Professional.String(), Name: "Professional"},
		&dreamboard.Category{Identifier: dreamboard.Financial.String(), Name: "Financial"},
		&dreamboard.Category{Identifier: dreamboard.Leisure.String(), Name: "Leisure"},
		&dreamboard.Category{Identifier: dreamboard.Emotional.String(), Name: "Emotional"},
		&dreamboard.Category{Identifier: dreamboard.Intellectual.String(), Name: "Intellectual"},
		&dreamboard.Category{Identifier: dreamboard.Spiritual.String(), Name: "Spiritual"},
		&dreamboard.Category{Identifier: dreamboard.Physical.String(), Name: "Physical"},
		&dreamboard.Category{Identifier: dreamboard.Intimate.String(), Name: "Intimate"},
		&dreamboard.Category{Identifier: dreamboard.Social.String(), Name: "Social"},
		&dreamboard.Category{Identifier: dreamboard.Familial.String(), Name: "Familial"},
	}

	return s.Repository.CreateCategories(ctx, categories)
}
