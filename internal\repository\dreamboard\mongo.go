package dreamboard

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection              *mongo.Collection
	trash                   *mongo.Collection
	dreamsCollection        *mongo.Collection // New: separate dreams collection
	categoriesCollection    *mongo.Collection // New: separate categories collection
	shareLinksCollection    *mongo.Collection
	contributionsCollection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection:              db.Collection(repository.DREAMBOARDS_COLLECTION),
		trash:                   db.Collection(repository.DREAMBOARDS_COLLECTION_TRASH),
		dreamsCollection:        db.Collection("dreamboards.dreams"),
		categoriesCollection:    db.Collection("dreamboards.categories"),
		shareLinksCollection:    db.Collection(repository.SHARELINKS_COLLECTION),
		contributionsCollection: db.Collection(repository.DREAMBOARDS_CONTRIBUTIONS_COLLECTION),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "user", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.user field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on categories._id field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "categories._id", Value: 1}},
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.categories._id field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on categories.identifier field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "categories.identifier", Value: 1}},
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.categories.identifier field")
		db.Client().Disconnect(context.Background())
	}

	// Create index on dreams._id field
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreams._id", Value: 1}},
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on dreamboard.dreams._id field")
		db.Client().Disconnect(context.Background())
	}

	// Create unique index on sharelinks code field
	_, err = repo.shareLinksCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "code", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on sharelink.code field")
	}

	// Create index on sharelinks dreamId field
	_, err = repo.shareLinksCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreamId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on sharelink.dreamId field")
	}

	// Create non-unique compound index on contributions dreamId and contributorUserId with a specific name
	_, err = repo.contributionsCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{
				{Key: "dreamId", Value: 1},
				{Key: "contributorUserId", Value: 1},
			},
			Options: options.Index().SetName("contributions_dreamId_contributorUserId_idx"), // Explicitly named non-unique index
		},
	)
	if err != nil {
		// More specific error handling can be added here if needed,
		// for example, checking if the error is due to the index already existing with the same name and spec.
		log.Printf("warning: failed to create non-unique index 'contributions_dreamId_contributorUserId_idx': %v", err)
	}

	// Create index on contributions contributorUserId field
	_, err = repo.contributionsCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "contributorUserId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on contribution.contributorUserId field")
	}

	// Create index on contributions dreamId field
	_, err = repo.contributionsCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreamId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on contribution.dreamId field")
	}

	// Create indexes for dreams collection
	_, err = repo.dreamsCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "dreamboardId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on dreams.dreamboardId field")
	}

	_, err = repo.dreamsCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{{Key: "userId", Value: 1}},
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on dreams.userId field")
	}

	// Create index for categories collection
	_, err = repo.categoriesCollection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "identifier", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on categories.identifier field")
	}

	return repo
}

// CRUD
func (m mongoDB) Create(ctx context.Context, dreamboard *dreamboard.Dreamboard) (string, error) {
	insertedResult, err := m.collection.InsertOne(ctx, dreamboard)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.NewConflictError(errors.Repository, "dreamboard conflict exists", errors.KeyDreamboardErrorConflict, err)
		}
		return "", errors.NewInternalError(errors.Repository, "dreamboard create failed", errors.KeyDreamboardErrorCreateFailed, err)
	}
	return insertedResult.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (m mongoDB) CreateDelete(ctx context.Context, deletedBoard *dreamboard.DeletedDreamboard) error {
	_, err := m.trash.InsertOne(ctx, deletedBoard)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "dreamboard delete create failed", errors.KeyDreamboardErrorDeleteCreateFailed, err)
	}
	return nil
}

func (m mongoDB) Find(ctx context.Context, id primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	var dreamboard dreamboard.Dreamboard
	err := m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: id}}).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "dreamboard find failed", errors.KeyDreamboardErrorFindFailed, err)
	}

	// Convert ObjectID to hex string for external use
	if dreamboard.ObjectID.IsZero() {
		return nil, errors.NewWithTranslationKey(errors.Repository, "dreamboard invalid id", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, nil)
	}

	dreamboard.ID = dreamboard.ObjectID.Hex()
	return &dreamboard, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	cursor, err := m.collection.Find(ctx, bson.D{})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "dreamboard find all failed", errors.KeyDreamboardErrorFindAllFailed, err)
	}
	defer cursor.Close(ctx)

	var boards []*dreamboard.Dreamboard
	for cursor.Next(ctx) {
		var dreamboard dreamboard.Dreamboard
		if err = cursor.Decode(&dreamboard); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "dreamboard decode failed", errors.KeyDreamboardErrorDecodeFailed, err)
		}
		dreamboard.ID = dreamboard.ObjectID.Hex()
		boards = append(boards, &dreamboard)
	}
	return boards, nil
}

func (m mongoDB) FindAllByUsers(ctx context.Context, userIDs []string) ([]*dreamboard.Dreamboard, error) {
	filter := bson.D{{Key: "user", Value: bson.D{{Key: "$in", Value: userIDs}}}}
	cursor, err := m.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "dreamboard find all failed", errors.KeyDreamboardErrorFindAllFailed, err)
	}
	defer cursor.Close(ctx)

	var boards []*dreamboard.Dreamboard
	for cursor.Next(ctx) {
		var dreamboard dreamboard.Dreamboard
		if err = cursor.Decode(&dreamboard); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "dreamboard decode failed", errors.KeyDreamboardErrorDecodeFailed, err)
		}
		dreamboard.ID = dreamboard.ObjectID.Hex()
		boards = append(boards, &dreamboard)
	}
	return boards, nil
}

func (m mongoDB) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	var dreamboard dreamboard.Dreamboard
	err := m.collection.FindOne(ctx, bson.D{{Key: "user", Value: userID}}).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "dreamboard find by user failed", errors.KeyDreamboardErrorFindByUserFailed, err)
	}
	dreamboard.ID = dreamboard.ObjectID.Hex()
	return &dreamboard, nil
}

func (m mongoDB) FindByDreamID(ctx context.Context, dreamID primitive.ObjectID) (*dreamboard.Dreamboard, error) {
	var dreamboard dreamboard.Dreamboard
	err := m.collection.FindOne(ctx, bson.D{{Key: "dreams._id", Value: dreamID}}).Decode(&dreamboard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "dreamboard find failed", errors.KeyDreamboardErrorFindFailed, err)
	}
	dreamboard.ID = dreamboard.ObjectID.Hex()
	return &dreamboard, nil
}

func (m mongoDB) FindDeletedByUser(ctx context.Context, userID string) (*dreamboard.DeletedDreamboard, error) {
	var deletedBoard dreamboard.DeletedDreamboard
	err := m.trash.FindOne(ctx, bson.D{{Key: "dreamboard.user", Value: userID}}).Decode(&deletedBoard)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "deleted dreamboard not found", errors.KeyDreamboardErrorDeletedNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "deleted dreamboard find failed", errors.KeyDreamboardErrorDeletedFindFailed, err)
	}
	deletedBoard.Dreamboard.ID = deletedBoard.Dreamboard.ObjectID.Hex()
	return &deletedBoard, nil
}

func (m mongoDB) Update(ctx context.Context, dreamboard *dreamboard.Dreamboard) error {
	if dreamboard.ObjectID.IsZero() {
		return errors.NewWithTranslationKey(errors.Repository, "dreamboard invalid id", errors.KeyDreamboardErrorInvalidId, errors.BadRequest, nil)
	}

	dreamboard.UpdatedAt = time.Now()

	opts := options.Update().SetUpsert(false)
	result, err := m.collection.UpdateOne(ctx,
		bson.D{{Key: "_id", Value: dreamboard.ObjectID}},
		bson.D{{Key: "$set", Value: dreamboard}},
		opts,
	)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.NewConflictError(errors.Repository, "dreamboard conflict update", errors.KeyDreamboardErrorConflictUpdate, err)
		}
		return errors.NewInternalError(errors.Repository, "dreamboard update failed", errors.KeyDreamboardErrorUpdateFailed, err)
	}

	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, nil)
	}
	return nil
}

func (m mongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: id}})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "dreamboard delete failed", errors.KeyDreamboardErrorDeleteFailed, err)
	}
	if result.DeletedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, nil)
	}
	return nil
}

// UpdateSummaryFields updates the calculated summary fields of a dreamboard
func (m mongoDB) UpdateSummaryFields(ctx context.Context, dreamboardID primitive.ObjectID, totalCost, savedAmount, monthlyNeeded, remainingAmount int) error {
	update := bson.M{
		"$set": bson.M{
			"totalDreamsCost": totalCost,
			"savedAmount":     savedAmount,
			"monthlyNeeded":   monthlyNeeded,
			"remainingAmount": remainingAmount,
			"updatedAt":       time.Now(),
		},
	}

	result, err := m.collection.UpdateOne(ctx, bson.M{"_id": dreamboardID}, update)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "dreamboard summary update failed", errors.KeyDreamboardErrorUpdateFailed, err)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "dreamboard not found", errors.KeyDreamboardErrorNotFound, nil)
	}
	return nil
}
