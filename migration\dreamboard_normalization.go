package migration

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// DreamboardNormalizationMigration handles the migration from embedded to normalized collections
type DreamboardNormalizationMigration struct {
	db *mongo.Database
}

// NewDreamboardNormalizationMigration creates a new migration instance
func NewDreamboardNormalizationMigration(db *mongo.Database) *DreamboardNormalizationMigration {
	return &DreamboardNormalizationMigration{db: db}
}

// Execute performs the migration
func (m *DreamboardNormalizationMigration) Execute(ctx context.Context) error {
	log.Println("Starting dreamboard normalization migration...")

	// Step 1: Backup existing data
	if err := m.backupExistingData(ctx); err != nil {
		return fmt.Errorf("failed to backup existing data: %w", err)
	}

	// Step 2: Create new collections with indexes
	if err := m.createCollectionsAndIndexes(ctx); err != nil {
		return fmt.Errorf("failed to create collections and indexes: %w", err)
	}

	// Step 3: Migrate dreamboards and extract dreams
	if err := m.migrateDreamboards(ctx); err != nil {
		return fmt.Errorf("failed to migrate dreamboards: %w", err)
	}

	log.Println("Dreamboard normalization migration completed successfully")
	return nil
}

// backupExistingData moves current dreamboards to backup collection
func (m *DreamboardNormalizationMigration) backupExistingData(ctx context.Context) error {
	log.Println("Backing up existing dreamboards...")

	dreamboardsCol := m.db.Collection("dreamboards")
	backupCol := m.db.Collection("dreamboards_backup_" + time.Now().Format("20060102_150405"))

	// Get all existing dreamboards
	cursor, err := dreamboardsCol.Find(ctx, bson.M{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var documents []interface{}
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			return err
		}
		documents = append(documents, doc)
	}

	if len(documents) > 0 {
		_, err = backupCol.InsertMany(ctx, documents)
		if err != nil {
			return err
		}
		log.Printf("Backed up %d dreamboards", len(documents))
	}

	return nil
}

// createCollectionsAndIndexes creates the new normalized collections with proper indexes
func (m *DreamboardNormalizationMigration) createCollectionsAndIndexes(ctx context.Context) error {
	log.Println("Creating collections and indexes...")

	// Create dreams collection with indexes
	dreamsCol := m.db.Collection("dreamboards.dreams")
	_, err := dreamsCol.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "dreamboardId", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "userId", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "dreamboardId", Value: 1}, {Key: "completed", Value: 1}},
		},
	})
	if err != nil {
		return fmt.Errorf("failed to create dreams indexes: %w", err)
	}

	// Create categories collection with index
	categoriesCol := m.db.Collection("dreamboards.categories")
	_, err = categoriesCol.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys: bson.D{{Key: "identifier", Value: 1}},
	})
	if err != nil {
		return fmt.Errorf("failed to create categories index: %w", err)
	}

	log.Println("Collections and indexes created successfully")
	return nil
}

// migrateDreamboards processes existing dreamboards and normalizes the data
func (m *DreamboardNormalizationMigration) migrateDreamboards(ctx context.Context) error {
	log.Println("Migrating dreamboards to normalized structure...")

	dreamboardsCol := m.db.Collection("dreamboards")
	dreamsCol := m.db.Collection("dreamboards.dreams")

	// Get all existing dreamboards
	cursor, err := dreamboardsCol.Find(ctx, bson.M{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var processedCount int
	var totalDreamsExtracted int

	for cursor.Next(ctx) {
		var oldDreamboard struct {
			ID              primitive.ObjectID     `bson:"_id"`
			User            string                 `bson:"user"`
			Dreams          []*dreamboard.Dream    `bson:"dreams"`
			Categories      []*dreamboard.Category `bson:"categories"`
			TotalDreamsCost int                    `bson:"totalDreamsCost"`
			SavedAmount     int                    `bson:"savedAmount"`
			MonthlyNeeded   int                    `bson:"monthlyNeeded"`
			RemainingAmount int                    `bson:"remainingAmount"`
			CreatedAt       time.Time              `bson:"createdAt"`
			UpdatedAt       time.Time              `bson:"updatedAt"`
		}

		if err := cursor.Decode(&oldDreamboard); err != nil {
			log.Printf("Error decoding dreamboard: %v", err)
			continue
		}

		// Extract and insert dreams
		if len(oldDreamboard.Dreams) > 0 {
			var dreamsToInsert []interface{}
			for _, dream := range oldDreamboard.Dreams {
				// Add foreign key reference and user ID
				dream.DreamboardID = oldDreamboard.ID
				dream.UserID = oldDreamboard.User

				// Ensure ObjectID is set
				if dream.ObjectID.IsZero() {
					dream.ObjectID = primitive.NewObjectID()
				}

				dreamsToInsert = append(dreamsToInsert, dream)
			}

			_, err = dreamsCol.InsertMany(ctx, dreamsToInsert)
			if err != nil {
				log.Printf("Error inserting dreams for dreamboard %s: %v", oldDreamboard.ID.Hex(), err)
				continue
			}
			totalDreamsExtracted += len(dreamsToInsert)
		}

		// Update dreamboard to remove embedded arrays
		newDreamboard := dreamboard.Dreamboard{
			ObjectID:        oldDreamboard.ID,
			User:            oldDreamboard.User,
			TotalDreamsCost: monetary.Amount(oldDreamboard.TotalDreamsCost),
			SavedAmount:     monetary.Amount(oldDreamboard.SavedAmount),
			MonthlyNeeded:   monetary.Amount(oldDreamboard.MonthlyNeeded),
			RemainingAmount: monetary.Amount(oldDreamboard.RemainingAmount),
			CreatedAt:       oldDreamboard.CreatedAt,
			UpdatedAt:       time.Now(), // Update timestamp for migration
		}

		// Replace the dreamboard document
		_, err = dreamboardsCol.ReplaceOne(ctx,
			bson.M{"_id": oldDreamboard.ID},
			newDreamboard,
		)
		if err != nil {
			log.Printf("Error updating dreamboard %s: %v", oldDreamboard.ID.Hex(), err)
			continue
		}

		processedCount++
	}

	log.Printf("Migration completed: %d dreamboards processed, %d dreams extracted",
		processedCount, totalDreamsExtracted)
	return nil
}

// Rollback reverts the migration by restoring from backup
func (m *DreamboardNormalizationMigration) Rollback(ctx context.Context, backupTimestamp string) error {
	log.Printf("Rolling back dreamboard normalization migration using backup: %s", backupTimestamp)

	backupCol := m.db.Collection("dreamboards_backup_" + backupTimestamp)
	dreamboardsCol := m.db.Collection("dreamboards")
	dreamsCol := m.db.Collection("dreamboards.dreams")

	// Clear current collections
	_, err := dreamboardsCol.DeleteMany(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to clear dreamboards collection: %w", err)
	}

	_, err = dreamsCol.DeleteMany(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to clear dreams collection: %w", err)
	}

	// Restore from backup
	cursor, err := backupCol.Find(ctx, bson.M{})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var documents []interface{}
	for cursor.Next(ctx) {
		var doc bson.M
		if err := cursor.Decode(&doc); err != nil {
			return err
		}
		documents = append(documents, doc)
	}

	if len(documents) > 0 {
		_, err = dreamboardsCol.InsertMany(ctx, documents)
		if err != nil {
			return err
		}
	}

	log.Println("Rollback completed successfully")
	return nil
}
