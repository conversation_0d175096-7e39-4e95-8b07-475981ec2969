package dreamboard

import (
	"time"

	"github.com/dinbora/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DreamboardDTO represents the response structure for API backward compatibility
// This DTO assembles data from multiple collections (dreamboards, dreams, categories)
// to maintain the same API response format as the previous embedded structure
type DreamboardDTO struct {
	ObjectID        primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID              string             `json:"id,omitempty" bson:"-"`
	User            string             `json:"user" bson:"user"`
	Categories      []*Category        `json:"categories" bson:"categories"`
	Dreams          []*Dream           `json:"dreams" bson:"dreams"`
	TotalDreamsCost monetary.Amount    `json:"totalDreamsCost" bson:"totalDreamsCost"`
	SavedAmount     monetary.Amount    `json:"savedAmount"`
	MonthlyNeeded   monetary.Amount    `json:"monthlyNeeded" bson:"monthlyNeeded"`
	RemainingAmount monetary.Amount    `json:"remainingAmount" bson:"remainingAmount"`
	CreatedAt       time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt       time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// FromDreamboard creates a DreamboardDTO from a Dreamboard and associated data
func (dto *DreamboardDTO) FromDreamboard(dreamboard *Dreamboard, dreams []*Dream, categories []*Category) {
	dto.ObjectID = dreamboard.ObjectID
	dto.ID = dreamboard.ID
	dto.User = dreamboard.User
	dto.Categories = categories
	dto.Dreams = dreams
	dto.TotalDreamsCost = dreamboard.TotalDreamsCost
	dto.SavedAmount = dreamboard.SavedAmount
	dto.MonthlyNeeded = dreamboard.MonthlyNeeded
	dto.RemainingAmount = dreamboard.RemainingAmount
	dto.CreatedAt = dreamboard.CreatedAt
	dto.UpdatedAt = dreamboard.UpdatedAt
}

// ToDreamboard converts DTO back to Dreamboard model (without embedded data)
func (dto *DreamboardDTO) ToDreamboard() *Dreamboard {
	return &Dreamboard{
		ObjectID:        dto.ObjectID,
		ID:              dto.ID,
		User:            dto.User,
		TotalDreamsCost: dto.TotalDreamsCost,
		SavedAmount:     dto.SavedAmount,
		MonthlyNeeded:   dto.MonthlyNeeded,
		RemainingAmount: dto.RemainingAmount,
		CreatedAt:       dto.CreatedAt,
		UpdatedAt:       dto.UpdatedAt,
	}
}

// ComputeTotals calculates and updates the total costs and savings fields for the DTO
func (dto *DreamboardDTO) ComputeTotals(dreams []*Dream) {
	var totalCost, monthlySum monetary.Amount
	for _, dream := range dreams {
		// Only consider active dreams
		if !dream.Completed {
			totalCost += dream.EstimatedCost
			monthlySum += dream.MonthlySavings
		}
	}
	dto.TotalDreamsCost = totalCost
	dto.MonthlyNeeded = monthlySum
	dto.RemainingAmount = dto.SavedAmount - dto.TotalDreamsCost
}

// CalculateSavedAmount calculates the saved amount for the DTO
func (dto *DreamboardDTO) CalculateSavedAmount(financialSheet interface{}, dreams []*Dream) {
	// Import the financialsheet package to access the Record type
	// For now, we'll use interface{} and handle the calculation in the service layer
	// This method is a placeholder to maintain API compatibility
}
