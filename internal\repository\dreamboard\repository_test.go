package dreamboard

import (
	"context"
	"testing"
	"time"

	"github.com/dinbora/dinbora-backend/internal/model/dreamboard"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestNormalizedStructure(t *testing.T) {
	// This test verifies that the new normalized structure works correctly
	// Note: This is a unit test that would need a test database setup

	t.Run("Dream model has required fields", func(t *testing.T) {
		dream := &dreamboard.Dream{
			DreamboardID:   primitive.NewObjectID(),
			UserID:         "test-user-123",
			Title:          "Test Dream",
			EstimatedCost:  10000,
			MonthlySavings: 500,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		}

		assert.NotEmpty(t, dream.DreamboardID)
		assert.NotEmpty(t, dream.UserID)
		assert.Equal(t, "Test Dream", dream.Title)
	})

	t.Run("Dreamboard model without embedded arrays", func(t *testing.T) {
		board := &dreamboard.Dreamboard{
			User:            "test-user-123",
			TotalDreamsCost: 10000,
			SavedAmount:     2500,
			MonthlyNeeded:   500,
			RemainingAmount: 7500,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		assert.Equal(t, "test-user-123", board.User)
		assert.Equal(t, 10000, int(board.TotalDreamsCost))
	})

	t.Run("DreamboardDTO assembles data correctly", func(t *testing.T) {
		// Create test data
		board := &dreamboard.Dreamboard{
			ObjectID:        primitive.NewObjectID(),
			User:            "test-user-123",
			TotalDreamsCost: 10000,
			SavedAmount:     2500,
			MonthlyNeeded:   500,
			RemainingAmount: 7500,
			CreatedAt:       time.Now(),
			UpdatedAt:       time.Now(),
		}

		dreams := []*dreamboard.Dream{
			{
				ObjectID:       primitive.NewObjectID(),
				DreamboardID:   board.ObjectID,
				UserID:         board.User,
				Title:          "Test Dream 1",
				EstimatedCost:  5000,
				MonthlySavings: 250,
				Completed:      false,
			},
			{
				ObjectID:       primitive.NewObjectID(),
				DreamboardID:   board.ObjectID,
				UserID:         board.User,
				Title:          "Test Dream 2",
				EstimatedCost:  5000,
				MonthlySavings: 250,
				Completed:      false,
			},
		}

		categories := []*dreamboard.Category{
			{
				ObjectID:   primitive.NewObjectID(),
				Identifier: "personal",
				Name:       "Personal",
				Color:      "#FF0000",
			},
		}

		// Create DTO
		dto := &dreamboard.DreamboardDTO{}
		dto.FromDreamboard(board, dreams, categories)

		// Verify DTO has all data
		assert.Equal(t, board.User, dto.User)
		assert.Len(t, dto.Dreams, 2)
		assert.Len(t, dto.Categories, 1)
		assert.Equal(t, "Test Dream 1", dto.Dreams[0].Title)
		assert.Equal(t, "Personal", dto.Categories[0].Name)

		// Test ComputeTotals
		dto.ComputeTotals(dto.Dreams)
		assert.Equal(t, 10000, int(dto.TotalDreamsCost))
		assert.Equal(t, 500, int(dto.MonthlyNeeded))

		// Test conversion back to Dreamboard
		result := dto.ToDreamboard()
		assert.Equal(t, board.User, result.User)
		assert.Equal(t, board.TotalDreamsCost, result.TotalDreamsCost)
	})
}

func TestRepositoryInterface(t *testing.T) {
	// This test verifies that the repository interface is correctly defined
	// Note: This would need actual repository implementation for integration testing

	t.Run("Repository interface has required methods", func(t *testing.T) {
		// This is a compile-time test to ensure interface is correctly defined
		var repo Repository

		// These should compile without errors
		_ = func() error {
			ctx := context.Background()
			objID := primitive.NewObjectID()

			// Test method signatures exist
			_, err := repo.Find(ctx, objID)
			if err != nil {
				return err
			}

			_, err = repo.FindDreamsByDreamboardID(ctx, objID)
			if err != nil {
				return err
			}

			_, err = repo.FindCategories(ctx)
			if err != nil {
				return err
			}

			return nil
		}

		assert.Nil(t, repo) // repo is nil but interface is correctly defined
	})
}
