{"apple": {"error": {"AccessTokenRequired": "Ops! Sua sessão expirou. Por favor, faça o login novamente.", "InvalidOboardingAgeRange": "Hmm, essa idade parece um pouco estranha. Que tal tentar de novo?", "InvalidOboardingFinancialSituation": "Parece que essa opção não é válida. Por favor, escolha uma da lista.", "InvalidOboardingFinancialGoal": "Parece que essa meta não é uma opção válida. Por favor, escolha uma da lista.", "InvalidOboardingPersonalInterest": "Parece que esse interesse não é uma opção válida. Por favor, escolha um da lista.", "FailedToProcessPhoto": "Ops! Algo deu errado com a sua foto. Vamos tentar com outra?", "InvalidFileType": "Hmm, este tipo de arquivo não funciona. Tente usar uma foto no formato JPG, JPEG, PNG ou HEIC.", "FileTooLarge": "Essa foto é um pouco pesada! Tente enviar uma imagem menor (até 5MB).", "InvalidLoginInput": "Opa! Parece que faltou preencher algo. Dê uma olhadinha nos campos e tente de novo.", "": "", "fetchFailed": "Ops! Não conseguimos obter suas informações do Apple. Tente novamente.", "decodeFailed": "Eita! Tivemos um probleminha para processar suas informações do Apple. Tente novamente.", "invalidToken": "Ops! Seu token de acesso expirou. Por favor, faça o login novamente.", "missingKid": "Ops! Não conseguimos encontrar a chave de segurança. Tente novamente.", "publicKeyParseFailed": "Eita! Tivemos um probleminha para processar a chave de segurança. Tente novamente."}}, "auth": {"error": {"invalidLoginInput": "Opa! Parece que faltou preencher algo. Dê uma olhadinha nos campos e tente de novo.", "loginValidationFailed": "Hmm, e-mail ou senha incorretos. Que tal tentar mais uma vez?", "invalidRegisterInput": "Opa! Parece que faltou preencher algo. Dê uma olhadinha em todos os campos para continuar.", "failedToParseFormData": "<PERSON><PERSON>, não conseguimos ler suas informações. Poderia tentar enviá-las de novo?", "invalidOboardingAgeRange": "Hmm, parece que há algo errado com a faixa etária. Pode verificar e tentar de novo?", "invalidOboardingFinancialSituation": "Opa, algo na sua situação financeira parece incorreto. Poderia verificar?", "invalidOboardingFinancialGoal": "Hmm, não entendemos sua meta financeira. Que tal revisar e tentar de novo?", "invalidOboardingPersonalInterest": "Parece que seus interesses não foram salvos. Vamos tentar mais uma vez!", "failedToProcessPhoto": "<PERSON>ão conseguimos carregar sua foto. Que tal tentar com outra imagem ou um pouco mais tarde?", "invalidFileType": "Tipo de arquivo não suportado! Por favor, envie uma imagem JPG, JPEG, PNG ou HEIC.", "fileTooLarge": "Essa foto é muito grande! Tente uma imagem com no máximo 5MB.", "invalidRefreshTokenInput": "<PERSON><PERSON>, algo inesperado aconteceu. Por favor, tente novamente.", "invalidForgotPasswordInput": "Hmm, algo não deu certo. Verifique seus dados e tente pedir a recuperação de novo.", "invalidResetPasswordInput": "Opa! Algo deu errado ao redefinir sua senha. Vamos tentar mais uma vez.", "invalidCheckPasswordInput": "Essa senha não parece certa. Dê uma olhadinha e tente de novo.", "userNotLoggedIn": "Parece que você não está conectado. Faça o login para continuar sua jornada!", "invalidAdminLoginInput": "Hmm, os dados de login do administrador não parecem corretos. Verifique e tente de novo.", "": "", "failedToUploadPhoto": "Ops, não conseguimos enviar sua foto. Que tal tentar com outra imagem?", "failedToCreateToken": "<PERSON><PERSON>, algo deu errado do nosso lado. Por favor, tente novamente em um instante.", "failedToRetrieveUserAfterCreation": "Sua conta foi criada, mas não conseguimos te conectar agora. Que tal tentar fazer login?", "brevoNotifierNotAvailable": "Ops! Não conseguimos enviar o e-mail agora. Por favor, tente novamente em alguns minutos.", "userNotAdmin": "Opa! Parece que esta é uma área restrita. Apenas para os mestres do jogo!", "userNotHR": "Opa! Parece que esta é uma área restrita. Apenas para os RH!", "invalidHRLoginInput": "Hmm, os dados de login do RH não parecem corretos. Verifique e tente de novo."}}, "progression": {"error": {"conflict": "Você já tem progresso salvo! Continue de onde parou.", "createFailed": "Ops! Não conseguimos salvar seu progresso. Tente novamente.", "invalidIdFormat": "ID de progresso inválido. Verifique os dados e tente novamente.", "invalidEvent": "Evento de progresso inválido. Verifique os dados e tente novamente.", "notFound": "Progresso não encontrado. Que tal começar uma nova jornada?", "findFailed": "Ops! Não conseguimos carregar seu progresso. Tente novamente.", "notFoundForUser": "Nenhum progresso encontrado. Comece sua jornada de aprendizado agora!", "findByUserFailed": "Ops! Não conseguimos carregar seu progresso. Tente novamente.", "updateConflict": "Não foi possível atualizar seu progresso. Tente novamente.", "updateFailed": "Ops! Não conseguimos salvar suas conquistas. Tente novamente.", "notFoundForUpdate": "Progresso não encontrado para atualização. Tente novamente.", "deleteFailed": "Ops! Não conseguimos resetar seu progresso. Tente novamente.", "notFoundForDeletion": "Progresso não encontrado para exclusão. Pode já ter sido removido.", "findTrailProgressionsFailed": "Ops! Não conseguimos carregar suas trilhas. Tente novamente."}}, "financialsheet": {"error": {"conflict": "Você já tem uma planilha financeira! Continue organizando suas finanças.", "createFailed": "Ops! Não conseguimos criar sua planilha financeira. Tente novamente.", "findFailed": "Ops! Não conseguimos carregar sua planilha financeira. Tente novamente.", "findAllFailed": "Ops! Não conseguimos <PERSON> as planilhas financeiras. Tente novamente.", "notFound": "Planilha financeira não encontrada. Que tal criar uma nova?", "invalidId": "ID da planilha financeira inválido. Verifique os dados e tente novamente.", "userNotFound": "Planilha financeira não encontrada para este usuário. Comece criando uma!", "findByUserFailed": "Ops! Não conseguimos carregar sua planilha financeira. Tente novamente.", "findByUsersFailed": "Ops! Não conseguimos <PERSON> as planilhas financeiras. Tente novamente.", "conflictUpdate": "Não foi possível atualizar sua planilha financeira. Tente novamente.", "updateFailed": "Ops! Não conseguimos salvar suas alterações. Tente novamente.", "deleteFailed": "Ops! Não conseguimos excluir sua planilha financeira. Tente novamente.", "invalidMonth": "Ops! O mês deve estar entre 1 e 12. Verifique e tente novamente.", "duplicateMonth": "Opa! Você já escolheu este mês. Selecione meses diferentes para a recorrência.", "sameMonthAsOriginal": "Não é possível repetir no mesmo mês da transação original. Escolha outros meses!", "recordAlreadyExists": "Você já tem uma planilha financeira! Continue organizando suas finanças.", "invalidRecord": "Ops! Dados inválidos. Verifique suas informações e tente novamente.", "noTransactionsAlreadyMarked": "Você já marcou 'sem transações' para hoje! Continue sua sequência amanhã.", "noTransactionsAlreadyMarkedDate": "Você já marc<PERSON> 'sem transações' para esta data. Escolha outro dia!", "cannotMarkSameDayAsTransaction": "Não é possível marcar 'sem transações' no mesmo dia de uma transação real.", "invalidFinancialSheetId": "ID da planilha financeira inválido. Verifique os dados e tente novamente.", "invalidMonthParameter": "Mês inválido! Deve estar entre 1 e 12. Verifique e tente novamente.", "invalidYearParameter": "Ano inválido! Verifique o formato e tente novamente.", "invalidInput": "Ops! Dados inválidos. Verifique as informações e tente novamente.", "validationFailed": "Alguns campos não estão preenchidos corretamente. Dê uma ol<PERSON><PERSON><PERSON>!", "dreamTransactionInput": "Ops! Não conseguimos processar os dados do seu sonho. Tente novamente.", "dreamTransactionValidation": "Alguns dados do seu sonho estão incorretos. Verifique e tente novamente.", "invalidTransactionId": "ID da transação inválido. Verifique os dados e tente novamente.", "invalidCategoryIdParam": "Ops! ID da categoria inválido no parâmetro. Verifique a URL e tente novamente.", "invalidMoneySource": "Ops! Fonte de dinheiro inválida. Escolha uma opção válida e tente novamente.", "invalidPaymentMethod": "Ops! Método de pagamento inválido. Selecione uma forma de pagamento válida.", "invalidCategoryIdentifier": "Identificador de categoria inválido. Verifique os dados e tente novamente.", "invalidCategoryType": "Tipo de categoria inválido. Escolha um tipo válido e tente novamente.", "invalidCategoryBackground": "Cor de fundo da categoria inválida. Selecione uma cor válida.", "invalidFinancialRecordId": "Ops! ID da planilha financeira inválido. Verifique os dados e tente novamente.", "invalidCategoryId": "Ops! ID da categoria inválido. Verifique os dados e tente novamente.", "cannotDeleteSystemCategories": "Opa! Não é possível excluir categorias do sistema. Elas são essenciais para o funcionamento!", "canOnlyDeleteOwnCategories": "Você só pode excluir suas próprias categorias. Esta não é sua!", "categoryInUse": "Esta categoria está sendo usada em transações! Remova as transações primeiro.", "transactionTypeMismatch": "Ops! O tipo da transação não combina com o tipo da categoria. Verifique e tente novamente.", "invalidTransaction": "Ops! Dados da transação inválidos. Verifique as informações e tente novamente.", "invalidDreamId": "Ops! ID do sonho inválido. Verifique os dados e tente novamente.", "transactionNotFound": "Transação não encontrada. Pode ter sido removida ou não existe.", "cannotMarkBeforeYesterday": "Não é possível marcar 'sem transações' para datas anteriores a ontem. Mantenha o foco no presente!"}, "category": {"error": {"conflict": "Esta categoria já existe! Escolha um nome diferente.", "createFailed": "Ops! Não conseguimos criar sua categoria. Tente novamente.", "findFailed": "Ops! Não conseguimos carregar suas categorias. Tente novamente.", "notFound": "Categoria não encontrada. Que tal criar uma nova?", "invalidId": "ID da categoria inválido. Verifique os dados e tente novamente.", "conflictUpdate": "Não foi possível atualizar sua categoria. Tente novamente.", "updateFailed": "Ops! Não conseguimo<PERSON> as alterações da categoria. Tente novamente.", "deleteFailed": "Ops! Não conseguimos excluir sua categoria. Tente novamente.", "findByIdFailed": "Ops! Não conseguimos encontrar esta categoria. Tente novamente.", "findByNameFailed": "Ops! Não conseguimos encontrar a categoria pelo nome. Tente novamente."}}}, "user": {"error": {"hashPassword": "<PERSON><PERSON>, tivemos um probleminha para salvar sua senha. Que tal tentar de novo?", "forbidden": "Opa! Parece que você precisa de uma chave especial para acessar aqui.", "invalidCredentials": "Hmm, essa combinação de e-mail e senha não parece certa. Vamos tentar de novo?", "resetPassword": "Opa! Algo deu errado ao tentar redefinir sua senha. Por favor, tente mais uma vez.", "mergeFailed": "<PERSON>h, tivemos um problema para atualizar suas informações. Poderia tentar novamente?", "processPassword": "<PERSON><PERSON>, tivemos um probleminha com a sua senha. Poderia tentar novamente?", "emailRequired": "<PERSON><PERSON>, faltou o e-mail! Por favor, preencha para continuar.", "invalidEmail": "Hmm, esse e-mail não parece válido. Que tal dar uma ol<PERSON><PERSON><PERSON>?", "emptyId": "Opa! Algo inesperado aconteceu do nosso lado. Por favor, tente novamente.", "nameRequired": "Quase lá! Só falta preencher seu nome.", "passwordRequired": "Não se esqueça da senha! Ela é super importante para proteger sua conta.", "referralCodeRequired": "Faltou o código de indicação! Preencha para continuar.", "setRoleNotAllowed": "Opa! Essa é uma ação superpoderosa que não pode ser feita por aqui.", "phoneRequired": "Opa, precisamos do seu telefone para continuar.", "passwordRequirements": "Para uma senha super segura, ela precisa ter:\n- <PERSON><PERSON> menos 6 caracteres\n- Uma letra mai<PERSON> (A-Z)\n- Uma letra minúscula (a-z)\n- <PERSON> (0-9)\n- Um caractere especial (!@#$)", "invalidPhoneNumber": "Ops! Número de telefone inválido. Verifique e tente novamente.", "": "", "conflict": "Este usuário já existe. Tente com um email diferente!", "notFoundById": "Usuário não encontrado. Verifique se o ID está correto.", "notFoundByEmail": "Nenhum usuário encontrado com este email. Que tal criar uma conta?", "notFoundByReferral": "Código de indicação inválido. Verifique se digitou corretamente.", "deletedNotFoundByEmail": "Nenhuma conta excluída encontrada com este email.", "conflictUpdate": "Não foi possível atualizar. Este email já está sendo usado por outro usuário.", "notFoundForUpdate": "Usuário não encontrado para atualização. Tente novamente.", "notFoundForDeletion": "Usuário não encontrado para exclusão. Pode já ter sido removido.", "createFailed": "Ops! Algo deu errado ao criar sua conta. Tente novamente.", "deletedCreateFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "findByIdFailed": "Ops! Algo deu errado ao buscar o usuário. Tente novamente.", "findAllFailed": "Ops! Algo deu errado ao carregar os usuários. Tente novamente.", "decodeUserFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "adminUsersNotFound": "Ops! Algo deu errado ao buscar administradores. Tente novamente.", "accessDenied": "Ops! Acesso negado. Você não tem permissão para executar esta ação.", "decodeAdminUserFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "findByEmailFailed": "Ops! Algo deu errado ao buscar o usuário. Tente novamente.", "findByReferralFailed": "Ops! Algo deu errado ao verificar o código. Tente novamente.", "findByReferringUserIdFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "cursorError": "Ops! Algo deu errado. Tente novamente mais tarde.", "findWithFilterFailed": "Ops! Algo deu errado ao buscar usuários. Tente novamente.", "deletedFindByEmailFailed": "Ops! Algo deu errado. Tente novamente mais tarde.", "invalidId": "ID do usuário inválido. Verifique os dados e tente novamente.", "updateFailed": "Ops! Algo deu errado ao atualizar. Tente novamente.", "deleteFailed": "Ops! Algo deu errado ao excluir. Tente novamente.", "deletedConflictExists": "Esta conta já foi excluída anteriormente."}}, "dreamboard": {"error": {"conflict": "Você já tem um quadro dos sonhos! Continue organizando seus objetivos.", "createFailed": "Ops! Não conseguimos criar seu quadro dos sonhos. Tente novamente.", "findFailed": "Ops! Não conseguimos carregar seu quadro dos sonhos. Tente novamente.", "notFound": "Quadro dos sonhos não encontrado. Que tal criar um novo?", "invalidId": "ID do quadro dos sonhos inválido. Verifique os dados e tente novamente.", "findAllFailed": "Ops! Não conseguimos carregar os quadros dos sonhos. Tente novamente.", "decodeFailed": "Ops! Algo deu errado ao processar os dados. Tente novamente.", "findByUserFailed": "Ops! Não conseguimos encontrar seu quadro dos sonhos. Tente novamente.", "conflictUpdate": "Não foi possível atualizar seu quadro dos sonhos. Tente novamente.", "updateFailed": "Ops! Não conseguimos salvar suas alterações. Tente novamente.", "deleteFailed": "Ops! Não conseguimos excluir seu quadro dos sonhos. Tente novamente.", "deleteCreateFailed": "Ops! Não conseguimos mover para a lixeira. Tente novamente.", "deletedNotFound": "Quadro dos sonhos excluído não encontrado. Pode já ter sido removido.", "deletedFindFailed": "Ops! Não conseguimos encontrar na lixeira. Tente novamente.", "dreamAddFailed": "Ops! Não conseguimos adicionar seu sonho. Tente novamente.", "dreamUpdateFailed": "Ops! Não conseguimos atualizar seu sonho. Tente novamente.", "dreamNotFound": "Sonho não encontrado. Pode ter sido removido ou não existe.", "dreamRemoveFailed": "Ops! Não conseguimos remover o sonho. Tente novamente.", "categoryAddFailed": "Ops! Não conseguimos adicionar a categoria. Tente novamente.", "categoryNotFound": "Categoria não encontrada. Pode ter sido removida ou não existe.", "categoryFindFailed": "Ops! Não conseguimos encontrar a categoria. Tente novamente.", "categoryUpdateFailed": "Ops! Não conseguimos atualizar a categoria. Tente novamente.", "categoryDeleteFailed": "Ops! Não conseguimos excluir a categoria. Tente novamente.", "categoryCreateFailed": "Ops! Não conseguimos criar a categoria. Tente novamente.", "sessionStartFailed": "Ops! Não conseguimos iniciar a operação. Tente novamente.", "transactionFailed": "Ops! Não conseguimos completar a operação. Tente novamente.", "shareLinkConflict": "Este link de compartilhamento já existe. Tente com outro código.", "shareLinkCreateFailed": "Ops! Não conseguimos criar o link de compartilhamento. Tente novamente.", "shareLinkNotFound": "Link de compartilhamento não encontrado. Pode ter expirado ou não existe.", "shareLinkFindFailed": "Ops! Não conseguimos encontrar o link. Tente novamente.", "shareLinkUpdateFailed": "Ops! Não conseguimos atualizar o link. Tente novamente.", "shareLinkDeleteFailed": "Ops! Não conseguimos excluir o link. Tente novamente.", "contributionCreateFailed": "Ops! Não conseguimos registrar sua contribuição. Tente novamente.", "contributionNotFound": "Contribuição não encontrada. Pode ter sido removida ou não existe.", "contributionFindFailed": "Ops! Não conseguimos encontrar as contribuições. Tente novamente.", "contributionDecodeFailed": "Ops! Algo deu errado ao processar as contribuições. Tente novamente.", "contributionCursorError": "Ops! Algo deu errado ao carregar as contribuições. Tente novamente.", "contributionUpdateFailed": "Ops! Não conseguimos atualizar a contribuição. Tente novamente.", "contributionStatusUpdateFailed": "Ops! Não conseguimos atualizar o status das contribuições. Tente novamente.", "contributionDeleteFailed": "Ops! Não conseguimos excluir a contribuição. Tente novamente.", "categoryIdentifierEmpty": "O identificador da categoria é obrigatório. Que tal escolher um nome único?", "categoryNameEmpty": "O nome da categoria é obrigatório. Dê um nome especial para ela!", "categoryIconEmpty": "O ícone da categoria é obrigatório. Escolha um ícone que represente bem!", "categoryColorEmpty": "A cor da categoria é obrigatória. Escolha uma cor que você goste!", "categoryIdentifierInvalid": "O identificador deve conter apenas letras minúsculas e números. Tente novamente!", "categoryColorInvalid": "A cor deve estar no formato hexadecimal (ex: #FF6347). Verifique e tente novamente!", "categoryUnmarshalFailed": "Ops! Não conseguimos processar os dados da categoria. Tente novamente.", "dreamCategoryInvalid": "A categoria do sonho é inválida. Escolha uma categoria válida!", "dreamTitleInvalid": "O título deve ter entre 1 e 100 caracteres. Que tal um título mais criativo?", "dreamTimeFrameInvalid": "O prazo selecionado é inválido. Escolha entre curto, médio ou longo prazo!", "dreamDeadlineInvalid": "A data limite deve ser pelo menos 24 horas no futuro. Escolha uma data mais adiante!", "dreamCostNegative": "O custo estimado não pode ser negativo. Insira um valor válido!", "dreamSavingsInvalid": "A economia mensal deve estar entre 0 e o custo total. Ajuste o valor!", "dreamMoneySourceInvalid": "A fonte de dinheiro selecionada é inválida. Escolha uma opção válida!", "dreamCreatorRequired": "O criador do sonho deve ser especificado para sonhos compartilhados.", "dreamFundingStatusInvalid": "O status de financiamento é inválido. Verifique os dados!", "dreamRaisedAmountNegative": "O valor arrecadado não pode ser negativo. Insira um valor válido!", "dreamboardUserRequired": "O usuário do quadro dos sonhos deve ser especificado.", "dreamboardDreamsInvalid": "A lista de sonhos é inválida. Verifique os dados!", "dreamboardCategoriesInvalid": "A lista de categorias é inválida. Verifique os dados!", "dreamboardDatesInvalid": "A data de criação não pode ser posterior à data de atualização.", "contributionDreamIdRequired": "O ID do sonho é obrigatório para contribuições.", "contributionUserIdRequired": "O ID do usuário contribuinte é obrigatório.", "contributionAmountNegative": "O valor da contribuição mensal não pode ser negativo.", "contributionStatusInvalid": "O status da contribuição é inválido. Verifique os dados!", "shareLinkDreamIdRequired": "O ID do sonho é obrigatório para links de compartilhamento.", "shareLinkCodeRequired": "O código do link deve ser especificado.", "shareLinkExpired": "A data de expiração deve estar no futuro.", "invalidInput": "Ops! Os dados enviados são inválidos. Verifique e tente novamente.", "categoryExists": "Esta categoria já existe! Que tal escolher um nome diferente?", "categoryInUse": "Esta categoria está sendo usada por um ou mais sonhos. Remova os sonhos primeiro!", "categoryNotInDreamboard": "Esta categoria não existe no seu quadro dos sonhos.", "alreadyExists": "Você já tem um quadro dos sonhos! Continue organizando seus objetivos.", "tokenGenerationFailed": "Ops! Não conseguimos gerar o código de convite. Tente novamente.", "inviteLinkDisabled": "Este link de convite está desabilitado. Peça um novo link!", "inviteLinkExpired": "Este link de convite expirou. Peça um novo link!", "userAlreadyContributing": "Você já está contribuindo para este sonho! Continue assim!", "missingParam": "Parâmetro obrigatório não encontrado. Verifique os dados!", "invalidType": "Tipo de parâmetro inválido. Deve ser 'personal' ou 'shared'.", "dreamIdRequired": "O ID do sonho é obrigatório. Verifique os dados!", "contributionIdRequired": "O ID da contribuição é obrigatório. Verifique os dados!", "codeRequired": "O código é obrigatório. Verifique os dados!", "unauthorized": "Você só pode gerenciar seus próprios sonhos e categorias.", "accessDenied": "Contribuição não encontrada ou acesso negado.", "validationRequired": "Código e valor da contribuição mensal são obrigatórios."}}, "google": {"error": {"accessTokenRequired": "Ops! Sua sessão expirou. Por favor, faça o login novamente.", "InvalidOboardingAgeRange ": "Hmm, essa idade parece um pouco estranha. Que tal tentar de novo?", "InvalidOboardingFinancialSituation": "Parece que essa opção não é válida. Por favor, escolha uma da lista.", "InvalidOboardingFinancialGoal": "Parece que essa meta não é uma opção válida. Por favor, escolha uma da lista.", "InvalidOboardingPersonalInterest": "Parece que esse interesse não é uma opção válida. Por favor, escolha um da lista.", "FailedToProcessPhoto": "Ops! Algo deu errado com a sua foto. Vamos tentar com outra?", "InvalidFileType": "Hmm, este tipo de arquivo não funciona. Tente usar uma foto no formato JPG, JPEG, PNG ou HEIC.", "FileTooLarge": "Essa foto é um pouco pesada! Tente enviar uma imagem menor (até 5MB).", "InvalidToken": "Ops! Sua sessão expirou. Por favor, faça o login novamente para continuar.", "": "", "fetchFailed": "Ops! Não conseguimos conectar. Verifique sua internet e tente novamente.", "decodeFailed": "Eita! Tivemos um probleminha para processar seus dados. Tente de novo em um instante."}}}