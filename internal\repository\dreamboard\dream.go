package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Dream Management - Updated for normalized structure
func (m mongoDB) CreateDream(ctx context.Context, dream *dreamboard.Dream) error {
	dream.ObjectID = primitive.NewObjectID()
	dream.CreatedAt = time.Now()
	dream.UpdatedAt = time.Now()

	_, err := m.dreamsCollection.InsertOne(ctx, dream)
	if err != nil {
		return errors.NewInternalError(errors.Repository, "dream create failed", errors.KeyDreamboardErrorDreamAddFailed, err)
	}

	// Convert ObjectID to hex string for external use
	dream.ID = dream.ObjectID.Hex()
	return nil
}

func (m mongoDB) FindDream(ctx context.Context, dreamID primitive.ObjectID) (*dreamboard.Dream, error) {
	var dream dreamboard.Dream
	err := m.dreamsCollection.FindOne(ctx, bson.M{"_id": dreamID}).Decode(&dream)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.NewNotFoundError(errors.Repository, "dream not found", errors.KeyDreamboardErrorDreamNotFound, err)
		}
		return nil, errors.NewInternalError(errors.Repository, "dream find failed", errors.KeyDreamboardErrorFindFailed, err)
	}
	dream.ID = dream.ObjectID.Hex()
	return &dream, nil
}

func (m mongoDB) UpdateDream(ctx context.Context, dream *dreamboard.Dream) error {
	dream.UpdatedAt = time.Now()

	result, err := m.dreamsCollection.UpdateOne(ctx,
		bson.M{"_id": dream.ObjectID},
		bson.M{"$set": dream})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "dream update failed", errors.KeyDreamboardErrorDreamUpdateFailed, err)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "dream not found", errors.KeyDreamboardErrorDreamNotFound, nil)
	}
	return nil
}

func (m mongoDB) DeleteDream(ctx context.Context, dreamID primitive.ObjectID) error {
	result, err := m.dreamsCollection.DeleteOne(ctx, bson.M{"_id": dreamID})
	if err != nil {
		return errors.NewInternalError(errors.Repository, "dream delete failed", errors.KeyDreamboardErrorDreamRemoveFailed, err)
	}
	if result.DeletedCount == 0 {
		return errors.NewNotFoundError(errors.Repository, "dream not found", errors.KeyDreamboardErrorDreamNotFound, nil)
	}
	return nil
}

// FindDreamsByDreamboardID finds all dreams for a specific dreamboard
func (m mongoDB) FindDreamsByDreamboardID(ctx context.Context, dreamboardID primitive.ObjectID) ([]*dreamboard.Dream, error) {
	cursor, err := m.dreamsCollection.Find(ctx, bson.M{"dreamboardId": dreamboardID})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "dreams find failed", errors.KeyDreamboardErrorFindFailed, err)
	}
	defer cursor.Close(ctx)

	var dreams []*dreamboard.Dream
	for cursor.Next(ctx) {
		var dream dreamboard.Dream
		if err := cursor.Decode(&dream); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "dream decode failed", errors.KeyDreamboardErrorFindFailed, err)
		}
		dream.ID = dream.ObjectID.Hex()
		dreams = append(dreams, &dream)
	}

	return dreams, nil
}

// FindDreamsByUserID finds all dreams for a specific user
func (m mongoDB) FindDreamsByUserID(ctx context.Context, userID string) ([]*dreamboard.Dream, error) {
	cursor, err := m.dreamsCollection.Find(ctx, bson.M{"userId": userID})
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "dreams find failed", errors.KeyDreamboardErrorFindFailed, err)
	}
	defer cursor.Close(ctx)

	var dreams []*dreamboard.Dream
	for cursor.Next(ctx) {
		var dream dreamboard.Dream
		if err := cursor.Decode(&dream); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "dream decode failed", errors.KeyDreamboardErrorFindFailed, err)
		}
		dream.ID = dream.ObjectID.Hex()
		dreams = append(dreams, &dream)
	}

	return dreams, nil
}

// FindActiveDreamsByDreamboardID finds all active (non-completed) dreams for a dreamboard
func (m mongoDB) FindActiveDreamsByDreamboardID(ctx context.Context, dreamboardID primitive.ObjectID) ([]*dreamboard.Dream, error) {
	filter := bson.M{
		"dreamboardId": dreamboardID,
		"completed":    false,
	}

	cursor, err := m.dreamsCollection.Find(ctx, filter)
	if err != nil {
		return nil, errors.NewInternalError(errors.Repository, "active dreams find failed", errors.KeyDreamboardErrorFindFailed, err)
	}
	defer cursor.Close(ctx)

	var dreams []*dreamboard.Dream
	for cursor.Next(ctx) {
		var dream dreamboard.Dream
		if err := cursor.Decode(&dream); err != nil {
			return nil, errors.NewInternalError(errors.Repository, "dream decode failed", errors.KeyDreamboardErrorFindFailed, err)
		}
		dream.ID = dream.ObjectID.Hex()
		dreams = append(dreams, &dream)
	}

	return dreams, nil
}
